@import "tailwindcss";
@import "tw-animate-css";

@custom-variant dark (&:is(.dark *));

:root {
  --background: rgb(253, 252, 255);
  --foreground: rgb(0, 0, 0);
  --card: rgb(247, 248, 248);
  --card-foreground: rgb(15, 20, 25);
  --popover: rgb(255, 255, 255);
  --popover-foreground: rgb(15, 20, 25);
  --primary: rgb(97, 63, 221);
  --primary-foreground: rgb(253, 252, 255);
  --secondary: rgb(15, 20, 25);
  --secondary-foreground: rgb(255, 255, 255);
  --muted: rgb(253, 252, 255);
  --muted-foreground: ;
  --accent: rgb(227, 236, 246);
  --accent-foreground: rgb(30, 157, 241);
  --destructive: rgb(244, 33, 46);
  --destructive-foreground: rgb(255, 255, 255);
  --border: rgb(225, 234, 239);
  --input: rgb(247, 249, 250);
  --ring: rgb(29, 161, 242);
  --chart-1: rgb(30, 157, 241);
  --chart-2: rgb(0, 184, 122);
  --chart-3: rgb(247, 185, 40);
  --chart-4: rgb(23, 191, 99);
  --chart-5: rgb(224, 36, 94);
  --sidebar: rgb(247, 248, 248);
  --sidebar-foreground: rgb(15, 20, 25);
  --sidebar-primary: rgb(30, 157, 241);
  --sidebar-primary-foreground: rgb(255, 255, 255);
  --sidebar-accent: rgb(227, 236, 246);
  --sidebar-accent-foreground: rgb(30, 157, 241);
  --sidebar-border: rgb(225, 232, 237);
  --sidebar-ring: rgb(29, 161, 242);
  --font-sans: DM Sans, sans-serif;
  --font-serif: DM Sans, sans-serif;
  --font-mono: DM Sans, sans-serif;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
}

.dark {
  --background: rgb(0, 0, 0);
  --foreground: rgb(231, 233, 234);
  --card: rgb(23, 24, 28);
  --card-foreground: rgb(217, 217, 217);
  --popover: rgb(0, 0, 0);
  --popover-foreground: rgb(231, 233, 234);
  --primary: rgb(28, 156, 240);
  --primary-foreground: rgb(255, 255, 255);
  --secondary: rgb(240, 243, 244);
  --secondary-foreground: rgb(15, 20, 25);
  --muted: rgb(24, 24, 24);
  --muted-foreground: rgb(114, 118, 122);
  --accent: rgb(6, 22, 34);
  --accent-foreground: rgb(28, 156, 240);
  --destructive: rgb(244, 33, 46);
  --destructive-foreground: rgb(255, 255, 255);
  --border: rgb(36, 38, 40);
  --input: rgb(34, 48, 60);
  --ring: rgb(29, 161, 242);
  --chart-1: rgb(30, 157, 241);
  --chart-2: rgb(0, 184, 122);
  --chart-3: rgb(247, 185, 40);
  --chart-4: rgb(23, 191, 99);
  --chart-5: rgb(224, 36, 94);
  --sidebar: rgb(23, 24, 28);
  --sidebar-foreground: rgb(217, 217, 217);
  --sidebar-primary: rgb(29, 161, 242);
  --sidebar-primary-foreground: rgb(255, 255, 255);
  --sidebar-accent: rgb(6, 22, 34);
  --sidebar-accent-foreground: rgb(28, 156, 240);
  --sidebar-border: rgb(56, 68, 77);
  --sidebar-ring: rgb(29, 161, 242);
  --font-sans: DM Sans, sans-serif;
  --font-serif: DM Sans, sans-serif;
  --font-mono: DM Sans, sans-serif;
  --radius: 1.3rem;
  --shadow-2xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xs: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-sm: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 1px 2px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-md: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 2px 4px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-lg: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 4px 6px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0),
    0px 8px 10px -1px hsl(202.8169 89.1213% 53.1373% / 0);
  --shadow-2xl: 0px 2px 0px 0px hsl(202.8169 89.1213% 53.1373% / 0);
}

@theme inline {
  --color-background: var(--background);
  --color-foreground: var(--foreground);
  --color-card: var(--card);
  --color-card-foreground: var(--card-foreground);
  --color-popover: var(--popover);
  --color-popover-foreground: var(--popover-foreground);
  --color-primary: var(--primary);
  --color-primary-foreground: var(--primary-foreground);
  --color-secondary: var(--secondary);
  --color-secondary-foreground: var(--secondary-foreground);
  --color-muted: var(--muted);
  --color-muted-foreground: var(--muted-foreground);
  --color-accent: var(--accent);
  --color-accent-foreground: var(--accent-foreground);
  --color-destructive: var(--destructive);
  --color-destructive-foreground: var(--destructive-foreground);
  --color-border: var(--border);
  --color-input: var(--input);
  --color-ring: var(--ring);
  --color-chart-1: var(--chart-1);
  --color-chart-2: var(--chart-2);
  --color-chart-3: var(--chart-3);
  --color-chart-4: var(--chart-4);
  --color-chart-5: var(--chart-5);
  --color-sidebar: var(--sidebar);
  --color-sidebar-foreground: var(--sidebar-foreground);
  --color-sidebar-primary: var(--sidebar-primary);
  --color-sidebar-primary-foreground: var(--sidebar-primary-foreground);
  --color-sidebar-accent: var(--sidebar-accent);
  --color-sidebar-accent-foreground: var(--sidebar-accent-foreground);
  --color-sidebar-border: var(--sidebar-border);
  --color-sidebar-ring: var(--sidebar-ring);

  --font-sans: var(--font-sans);
  --font-mono: var(--font-mono);
  --font-serif: var(--font-serif);

  --radius-sm: calc(var(--radius) - 4px);
  --radius-md: calc(var(--radius) - 2px);
  --radius-lg: var(--radius);
  --radius-xl: calc(var(--radius) + 4px);

  --shadow-2xs: var(--shadow-2xs);
  --shadow-xs: var(--shadow-xs);
  --shadow-sm: var(--shadow-sm);
  --shadow: var(--shadow);
  --shadow-md: var(--shadow-md);
  --shadow-lg: var(--shadow-lg);
  --shadow-xl: var(--shadow-xl);
  --shadow-2xl: var(--shadow-2xl);
}

@media (prefers-color-scheme: dark) {
  :root {
    --background-primary: #fdfcff;
  }
}

html {
  background: var(--background);
}

body {
  min-height: 100vh;
}

/* Ensure all screens inherit the background color */
#__next,
[data-nextjs-scroll-focus-boundary] {
  background: var(--background);
  min-height: 100vh;
}

.dark {
  --background: oklch(0.145 0 0);
  --foreground: oklch(0.985 0 0);
  --card: oklch(0.205 0 0);
  --card-foreground: oklch(0.985 0 0);
  --popover: oklch(0.205 0 0);
  --popover-foreground: oklch(0.985 0 0);
  --primary: oklch(0.922 0 0);
  --primary-foreground: oklch(0.205 0 0);
  --secondary: oklch(0.269 0 0);
  --secondary-foreground: oklch(0.985 0 0);
  --muted: oklch(0.269 0 0);
  --muted-foreground: oklch(0.708 0 0);
  --accent: oklch(0.269 0 0);
  --accent-foreground: oklch(0.985 0 0);
  --destructive: oklch(0.704 0.191 22.216);
  --border: oklch(1 0 0 / 10%);
  --input: oklch(1 0 0 / 15%);
  --ring: oklch(0.556 0 0);
  --chart-1: oklch(0.488 0.243 264.376);
  --chart-2: oklch(0.696 0.17 162.48);
  --chart-3: oklch(0.769 0.188 70.08);
  --chart-4: oklch(0.627 0.265 303.9);
  --chart-5: oklch(0.645 0.246 16.439);
  --sidebar: oklch(0.205 0 0);
  --sidebar-foreground: oklch(0.985 0 0);
  --sidebar-primary: oklch(0.488 0.243 264.376);
  --sidebar-primary-foreground: oklch(0.985 0 0);
  --sidebar-accent: oklch(0.269 0 0);
  --sidebar-accent-foreground: oklch(0.985 0 0);
  --sidebar-border: oklch(1 0 0 / 10%);
  --sidebar-ring: oklch(0.556 0 0);
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  body {
    @apply bg-background text-foreground;
  }
}
