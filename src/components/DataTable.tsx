import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

const websites = [
  {
    website: "www.example.com",
    country: "USA",
    language: "United States",
    category: "Computer & Electronics",
    otherCategory: "Entertainment",
    greyNiches: ["💻", "🎮", "📱", "🎵", "🎬"],
  },
  {
    website: "www.techstore.com",
    country: "Canada",
    language: "English",
    category: "Technology",
    otherCategory: "Gaming",
    greyNiches: ["⚡", "🔧", "💡", "🎯", "🚀"],
  },
  {
    website: "www.fashionhub.com",
    country: "UK",
    language: "English",
    category: "Fashion & Beauty",
    otherCategory: "Lifestyle",
    greyNiches: ["👗", "💄", "👠", "💍", "🌟"],
  },
  {
    website: "www.foodie.com",
    country: "Australia",
    language: "English",
    category: "Food & Beverage",
    otherCategory: "Health",
    greyNiches: ["🍕", "🥗", "☕", "🍰", "🥑"],
  },
  {
    website: "www.travelblog.com",
    country: "Germany",
    language: "German",
    category: "Travel & Tourism",
    otherCategory: "Adventure",
    greyNiches: ["✈️", "🏔️", "🏖️", "📸", "🗺️"],
  },
];

export function DataTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Website</TableHead>
          <TableHead>Country</TableHead>
          <TableHead>Language</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Other categories</TableHead>
          <TableHead>Grey niches</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {websites.map((website, index) => (
          <TableRow key={index}>
            <TableCell>{website.website}</TableCell>
            <TableCell>{website.country}</TableCell>
            <TableCell>{website.language}</TableCell>
            <TableCell>{website.category}</TableCell>
            <TableCell>{website.otherCategory}</TableCell>
            <TableCell>
              <div className="flex gap-1 justify-end">
                {website.greyNiches.map((icon, iconIndex) => (
                  <span key={iconIndex} className="text-sm">
                    {icon}
                  </span>
                ))}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
      {/* <TableFooter>
        <TableRow>
          <TableCell colSpan={5}>Total Websites</TableCell>
          <TableCell className="text-right">{websites.length}</TableCell>
        </TableRow>
      </TableFooter> */}
    </Table>
  );
}
