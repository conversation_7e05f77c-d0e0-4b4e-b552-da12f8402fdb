import {
  Table,
  TableBody,
  TableCaption,
  TableCell,
  TableFooter,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import {
  FaLaptop,
  FaGamepad,
  FaMobileAlt,
  FaMusic,
  FaFilm,
  FaBolt,
  FaWrench,
  FaLightbulb,
  FaBullseye,
  FaRocket,
  FaTshirt,
  FaPalette,
  FaHeart,
  FaGem,
  FaStar,
  FaUtensils,
  FaLeaf,
  FaCoffee,
  FaBirthdayCake,
  FaSeedling,
  FaPlane,
  FaMountain,
  FaUmbrellaBeach,
  FaCamera,
  FaMap,
} from "react-icons/fa";

const websites = [
  {
    website: "www.example.com",
    country: "USA",
    language: "United States",
    category: "Computer & Electronics",
    otherCategory: "Entertainment",
    greyNiches: [FaLaptop, FaGamepad, FaMobileAlt, FaMusic, FaFilm],
  },
  {
    website: "www.techstore.com",
    country: "Canada",
    language: "English",
    category: "Technology",
    otherCategory: "Gaming",
    greyNiches: [FaBolt, FaWrench, FaLightbulb, FaBullseye, FaRocket],
  },
  {
    website: "www.fashionhub.com",
    country: "UK",
    language: "English",
    category: "Fashion & Beauty",
    otherCategory: "Lifestyle",
    greyNiches: [FaTshirt, FaPalette, FaHeart, FaGem, FaStar],
  },
  {
    website: "www.foodie.com",
    country: "Australia",
    language: "English",
    category: "Food & Beverage",
    otherCategory: "Health",
    greyNiches: [FaUtensils, FaLeaf, FaCoffee, FaBirthdayCake, FaSeedling],
  },
  {
    website: "www.travelblog.com",
    country: "Germany",
    language: "German",
    category: "Travel & Tourism",
    otherCategory: "Adventure",
    greyNiches: [FaPlane, FaMountain, FaUmbrellaBeach, FaCamera, FaMap],
  },
];

export function DataTable() {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Website</TableHead>
          <TableHead>Country</TableHead>
          <TableHead>Language</TableHead>
          <TableHead>Category</TableHead>
          <TableHead>Other categories</TableHead>
          <TableHead>Grey niches</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {websites.map((website, index) => (
          <TableRow key={index}>
            <TableCell>{website.website}</TableCell>
            <TableCell>{website.country}</TableCell>
            <TableCell>{website.language}</TableCell>
            <TableCell>{website.category}</TableCell>
            <TableCell>{website.otherCategory}</TableCell>
            <TableCell>
              <div className="flex gap-2">
                {website.greyNiches.map((IconComponent, iconIndex) => (
                  <span key={iconIndex} className="text-sm text-gray-600">
                    <IconComponent />
                  </span>
                ))}
              </div>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
      {/* <TableFooter>
        <TableRow>
          <TableCell colSpan={5}>Total Websites</TableCell>
          <TableCell className="text-right">{websites.length}</TableCell>
        </TableRow>
      </TableFooter> */}
    </Table>
  );
}
